"""
Create working users for login testing.

This script creates users that can be used for login testing without dealing with RBAC issues.
"""

import os
import sys
import uuid
from datetime import datetime
from werkzeug.security import generate_password_hash

def create_working_users():
    """Create working users for login testing."""
    try:
        from flask import current_app
        from models.models import db, User, Restaurant
        
        print("🔧 Creating working users for login testing...")
        
        # 1. Create a test restaurant
        print("\n📋 Creating test restaurant...")
        restaurant = Restaurant(
            id=str(uuid.uuid4()),
            name="Test Restaurant",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.session.add(restaurant)
        db.session.flush()  # Get the restaurant ID
        print(f"✅ Created restaurant: {restaurant.name} ({restaurant.id})")
        
        # 2. Create test users
        users_to_create = [
            {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "password123"
            },
            {
                "username": "avmaman7",
                "email": "<EMAIL>",
                "password": "palermo"
            },
            {
                "username": "demo",
                "email": "<EMAIL>",
                "password": "demo123"
            }
        ]
        
        created_users = []
        
        for user_data in users_to_create:
            print(f"\n📋 Creating user: {user_data['email']}...")
            
            # Check if user already exists
            existing_user = User.query.filter_by(email=user_data['email']).first()
            if existing_user:
                print(f"User {user_data['email']} already exists, updating password...")
                existing_user.password_hash = generate_password_hash(user_data['password'])
                existing_user.restaurant_id = restaurant.id
                existing_user.is_active = True
                user = existing_user
            else:
                user = User(
                    id=str(uuid.uuid4()),
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=generate_password_hash(user_data['password']),
                    restaurant_id=restaurant.id,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    is_active=True
                )
                db.session.add(user)
            
            created_users.append((user, user_data['password']))
            print(f"✅ User {user_data['email']} ready")
        
        # 3. Commit all changes
        db.session.commit()
        print(f"\n✅ Successfully created/updated {len(created_users)} users")
        
        # 4. Test password verification for all users
        print(f"\n🔍 Testing password verification...")
        from werkzeug.security import check_password_hash
        
        all_tests_passed = True
        for user, password in created_users:
            test_result = check_password_hash(user.password_hash, password)
            status = "✅ PASS" if test_result else "❌ FAIL"
            print(f"  {user.email}: {status}")
            if not test_result:
                all_tests_passed = False
        
        if all_tests_passed:
            print("\n🎉 All users created successfully and passwords verified!")
            print("\nYou can now log in with any of these credentials:")
            for user, password in created_users:
                print(f"  Email: {user.email}, Password: {password}")
        else:
            print("\n⚠️ Warning: Password verification failed for some users")
        
        # 5. Also test the admin user
        print(f"\n🔍 Testing existing admin user...")
        admin_user = User.query.filter_by(email="<EMAIL>").first()
        if admin_user:
            admin_test = check_password_hash(admin_user.password_hash, "admin123")
            status = "✅ PASS" if admin_test else "❌ FAIL"
            print(f"  <EMAIL>: {status}")
            if admin_test:
                print("  Email: <EMAIL>, Password: admin123")
        else:
            print("  Admin user not found")
        
    except Exception as e:
        print(f"\n❌ Error creating working users: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # Import the app
    from app import app
    
    # Use the application context
    with app.app_context():
        create_working_users()
