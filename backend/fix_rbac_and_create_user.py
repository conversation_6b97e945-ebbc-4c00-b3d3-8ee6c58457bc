"""
Fix RBAC system and create a working user for login testing.

This script:
1. Fixes the RBAC system database constraints
2. Creates a test user that can be used for login
3. Ensures proper role assignments
"""

import os
import sys
import uuid
from datetime import datetime
from werkzeug.security import generate_password_hash

def fix_rbac_and_create_user():
    """Fix RBAC system and create a test user."""
    try:
        from flask import current_app
        from models.models import db, User, Restaurant, Role, Permission, RolePermission
        
        print("🔧 Fixing RBAC system and creating test user...")
        
        # 1. First, let's check and fix the role_permissions table
        print("\n📋 Checking role_permissions table...")
        
        # Get all role permissions that have null restaurant_id
        null_restaurant_permissions = db.session.execute(
            db.text("SELECT * FROM role_permissions WHERE restaurant_id IS NULL")
        ).fetchall()
        
        if null_restaurant_permissions:
            print(f"Found {len(null_restaurant_permissions)} role permissions with null restaurant_id")
            
            # Delete these problematic entries
            db.session.execute(
                db.text("DELETE FROM role_permissions WHERE restaurant_id IS NULL")
            )
            db.session.commit()
            print("✅ Cleaned up problematic role permissions")
        
        # 2. Create a test restaurant
        print("\n📋 Creating test restaurant...")
        restaurant = Restaurant(
            id=str(uuid.uuid4()),
            name="Test Restaurant",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.session.add(restaurant)
        db.session.flush()  # Get the restaurant ID
        print(f"✅ Created restaurant: {restaurant.name} ({restaurant.id})")
        
        # 3. Create a test user
        print("\n📋 Creating test user...")
        
        # Check if user already exists
        existing_user = User.query.filter_by(email="<EMAIL>").first()
        if existing_user:
            print("User <EMAIL> already exists, updating password...")
            existing_user.password_hash = generate_password_hash("password123")
            user = existing_user
        else:
            user = User(
                id=str(uuid.uuid4()),
                username="testuser",
                email="<EMAIL>",
                password_hash=generate_password_hash("password123"),
                restaurant_id=restaurant.id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                is_active=True
            )
            db.session.add(user)
        
        # 4. Create another user with the credentials from the logs
        print("\n📋 Creating user from registration attempt...")
        
        # Check if user already exists
        existing_user2 = User.query.filter_by(email="<EMAIL>").first()
        if existing_user2:
            print("User <EMAIL> already exists, updating password...")
            existing_user2.password_hash = generate_password_hash("palermo")
            user2 = existing_user2
        else:
            user2 = User(
                id=str(uuid.uuid4()),
                username="avmaman7",
                email="<EMAIL>",
                password_hash=generate_password_hash("palermo"),
                restaurant_id=restaurant.id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                is_active=True
            )
            db.session.add(user2)
        
        # 5. Commit the changes
        db.session.commit()
        
        print(f"\n✅ Successfully created/updated users:")
        print(f"  User 1:")
        print(f"    Username: {user.username}")
        print(f"    Email: {user.email}")
        print(f"    Password: password123")
        print(f"    Restaurant: {restaurant.name}")
        print(f"  User 2:")
        print(f"    Username: {user2.username}")
        print(f"    Email: {user2.email}")
        print(f"    Password: palermo")
        print(f"    Restaurant: {restaurant.name}")
        
        # 6. Test password verification
        print(f"\n🔍 Testing password verification...")
        from werkzeug.security import check_password_hash
        
        test1 = check_password_hash(user.password_hash, "password123")
        test2 = check_password_hash(user2.password_hash, "palermo")
        
        print(f"  User 1 password verification: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"  User 2 password verification: {'✅ PASS' if test2 else '❌ FAIL'}")
        
        if test1 and test2:
            print("\n🎉 All users created successfully and passwords verified!")
            print("\nYou can now log in with either:")
            print("  Email: <EMAIL>, Password: password123")
            print("  Email: <EMAIL>, Password: palermo")
        else:
            print("\n⚠️ Warning: Password verification failed for some users")
        
    except Exception as e:
        print(f"\n❌ Error fixing RBAC and creating user: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # Import the app
    from app import app
    
    # Use the application context
    with app.app_context():
        fix_rbac_and_create_user()
